<nz-layout class="app-layout">
  @if (!isMobile()) {
    <nz-sider
      class="menu-sidebar"
      nzWidth="256px"
      nzCollapsible
      [(nzCollapsed)]="isCollapsed"
      [nzTrigger]="null"
    >
      <div class="sidebar-logo">
        <a [routerLink]="'/dashboard'" class="w-100">
          <img
            [class]="isCollapsed() ? 'logo-collapsed' : 'logo'"
            alt="logo"
            (click)="onLogoClick()"
            src="/assets/img/logos/logo.svg"
          />
        </a>
        <a
          class="trigger"
          [class]="{ collapsed: isCollapsed() }"
          (click)="onSiderCollapse()"
        >
          @if (isCollapsed()) {
            <i nz-icon nzType="biuti-ui:open-menu"></i>
          } @else {
            <i nz-icon nzType="biuti-ui:close-menu" class="fa-rotate-90"></i>
          }
        </a>
      </div>

      <ul nz-menu class="main-menu" nzTheme="light" [nzMode]="'inline'">
        <ng-container
          *ngTemplateOutlet="menuTpl; context: { menus: menus }"
        ></ng-container>
        <ng-template #menuTpl let-menus="menus">
          @for (menu of menus(); track menu.id) {
            @if (!menu.hidden) {
              @if (menu.divider?.enabled) {
                <nz-divider
                  style="margin: 0"
                  [nzText]="
                    !isCollapsed() ? (menu.divider.text | translate) : null
                  "
                  [nzOrientation]="'right'"
                />
              }

              @if (!menu.children) {
                <li
                  *rolePermission="menu.rolePermission"
                  nz-menu-item
                  [class]="{
                    'menu-item': true,
                    'is-collapsed': menu.bottomPosition && isCollapsed(),
                    'help-center': menu.bottomPosition,
                  }"
                  [nzSelected]="menu.selected"
                  [nzMatchRouterExact]="true"
                  [nzDisabled]="menu.disabled"
                  nz-tooltip
                  [nzTooltipTitle]="
                    isCollapsed() && menu.level == 0
                      ? (menu.title | translate)
                      : null
                  "
                  nzTooltipPlacement="right"
                  (click)="!menu.disabled ? onMenuClick(menu) : null"
                >
                  @if (menu.icon) {
                    <i nz-icon [nzType]="menu.icon"></i>
                  }

                  <span
                    [style]="
                      !isCollapsed() && menu.level != 0
                        ? 'margin-left: 2.6rem'
                        : ''
                    "
                    >{{ menu.title | translate }}</span
                  >
                </li>
              } @else {
                <li
                  *rolePermission="menu.rolePermission"
                  class="menu-item"
                  [style]="isCollapsed() ? { 'margin-left': '0' } : {}"
                  nz-submenu
                  [nzOpen]="menu.open"
                  [nzTitle]="menu.title | translate"
                  [nzIcon]="menu.icon"
                  [nzDisabled]="menu.disabled"
                >
                  <ng-container
                    *ngTemplateOutlet="
                      menuTpl;
                      context: {
                        menus: menu.children,
                      }
                    "
                  ></ng-container>
                </li>
              }
            }
          }
        </ng-template>
      </ul>
    </nz-sider>
  }
  <nz-layout>
    <nz-header>
      <div class="app-header">
        <app-top-bar />
      </div>
    </nz-header>
    <nz-content>
      <div id="triangle-topleft"></div>
      <div id="circle"></div>
      <div class="inner-content">
        <app-header></app-header>
        <ng-container *ngTemplateOutlet="tplContent()"></ng-container>
      </div>
    </nz-content>
    <app-footer></app-footer>
  </nz-layout>
</nz-layout>
