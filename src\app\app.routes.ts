import { Routes } from '@angular/router';
import { roleType } from '@models/enums/role';
import { Dashboard } from '@pages/dashboard/dashboard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: Dashboard,
    // canActivate: [authGuard, subscriptionGuard],
    data: { preload: true, rolePermission: [roleType.admin] },
  },
];
