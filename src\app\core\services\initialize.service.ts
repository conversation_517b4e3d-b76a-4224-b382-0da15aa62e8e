/** Core */
import { Location } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import { AuthService } from './http/auth.service';
import { SocketService } from './socket.service';
import { LanguageService } from './utils/language';
import { ThemeService } from './utils/theme';

const ROUTES_NO_LOGOUT = ['/auth/activate-account', '/auth/reset-password'];

@Injectable({ providedIn: 'root' })
export class InitializeService {
  // SERVICES
  private authService = inject(AuthService);
  private location = inject(Location);
  private socketService = inject(SocketService);
  private languageService = inject(LanguageService);
  private themeService = inject(ThemeService);

  initThemeAndLanguage(): Promise<any> {
    this.languageService.configureLang(environment.languages);
    return Promise.all([
      this.themeService.loadTheme(),
      this.languageService.loadLanguage(),
    ]);
  }

  async initConfigApp(): Promise<any> {
    await new Promise(async (resolve, reject) => {
      const token = localStorage.getItem(GenericUtils.session_token);
      const adminId = localStorage.getItem(GenericUtils.user_id);

      if (!!token && !!adminId) {
        // GET SETTINGS AND STAFF PROFILE
        this.authService.getStaffProfile(adminId).subscribe({
          next: () => {
            this.authService.setLoggedIn(true);
            this.socketService.initSocket();
            resolve(true);
          },
          error: () => {
            this.authService.logout().subscribe({
              next: () => {
                resolve(true);
              },
            });
          },
        });
      } else {
        const currentUrl = this.location.path();

        if (ROUTES_NO_LOGOUT.find((item) => currentUrl.startsWith(item))) {
        } else {
          this.authService.logout().subscribe({});
        }
        resolve(true);
      }
    });
    // Initialize theme and language
    return this.initThemeAndLanguage();
  }
}
